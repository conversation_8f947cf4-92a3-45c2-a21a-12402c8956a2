Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "dotnetlearner", "dotnetlearner.csproj", "{57DB4E3C-A116-44AD-0A2D-FCA0BCCF64D7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{57DB4E3C-A116-44AD-0A2D-FCA0BCCF64D7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57DB4E3C-A116-44AD-0A2D-FCA0BCCF64D7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{57DB4E3C-A116-44AD-0A2D-FCA0BCCF64D7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57DB4E3C-A116-44AD-0A2D-FCA0BCCF64D7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {459CB050-FED6-4EBC-96BE-7F8E0BAB35AE}
	EndGlobalSection
EndGlobal
